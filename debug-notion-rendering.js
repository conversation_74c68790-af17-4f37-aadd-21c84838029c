/**
 * Notion渲染调试脚本
 * 
 * 用于检查Mermaid、KaTeX和数据库视图的渲染状态
 * 在浏览器控制台中运行此脚本来诊断问题
 * 
 * @since 2.0.0-beta.1
 * @package Notion_To_WordPress
 */

(function() {
    'use strict';
    
    console.log('🔍 [Notion渲染调试] 开始诊断...');
    
    // 检查资源优化器状态
    function checkResourceOptimizer() {
        console.log('\n📦 [资源优化器检查]');
        
        if (typeof window.NotionResourceOptimizer !== 'undefined') {
            console.log('✅ 资源优化器已加载');
            
            // 检查压缩配置
            const scripts = document.querySelectorAll('script[src*="resource-optimizer"]');
            if (scripts.length > 0) {
                console.log('✅ 资源优化器脚本已加载');
                console.log('ℹ️ 压缩功能应该已被禁用以保护Mermaid和KaTeX');
            }
        } else {
            console.log('❌ 资源优化器未找到');
        }
    }
    
    // 检查KaTeX状态
    function checkKaTeX() {
        console.log('\n🧮 [KaTeX检查]');
        
        const katexLoaded = typeof window.katex !== 'undefined';
        console.log(katexLoaded ? '✅ KaTeX库已加载' : '❌ KaTeX库未加载');
        
        const equations = document.querySelectorAll('.notion-equation-inline, .notion-equation-block');
        console.log(`📊 找到 ${equations.length} 个数学公式元素`);
        
        if (equations.length > 0) {
            let renderedCount = 0;
            equations.forEach(eq => {
                if (eq.querySelector('.katex')) {
                    renderedCount++;
                }
            });
            console.log(`✅ 已渲染: ${renderedCount}/${equations.length} 个公式`);
        }
        
        // 检查CSS
        const latexCSS = document.querySelector('link[href*="latex-styles.css"]');
        console.log(latexCSS ? '✅ LaTeX样式CSS已加载' : '❌ LaTeX样式CSS未找到');
    }
    
    // 检查Mermaid状态
    function checkMermaid() {
        console.log('\n📊 [Mermaid检查]');
        
        const mermaidLoaded = typeof window.mermaid !== 'undefined';
        console.log(mermaidLoaded ? '✅ Mermaid库已加载' : '❌ Mermaid库未加载');
        
        const mermaidElements = document.querySelectorAll('.mermaid, pre.mermaid, pre code.language-mermaid');
        console.log(`📊 找到 ${mermaidElements.length} 个Mermaid图表元素`);
        
        if (mermaidElements.length > 0) {
            let renderedCount = 0;
            mermaidElements.forEach(elem => {
                if (elem.querySelector('svg')) {
                    renderedCount++;
                }
            });
            console.log(`✅ 已渲染: ${renderedCount}/${mermaidElements.length} 个图表`);
        }
    }
    
    // 检查数据库视图状态
    function checkDatabaseViews() {
        console.log('\n🗃️ [数据库视图检查]');
        
        const databases = document.querySelectorAll('.notion-database, .notion-child-database');
        console.log(`📊 找到 ${databases.length} 个数据库视图`);
        
        const databaseCSS = document.querySelector('link[href*="notion-database.css"]');
        console.log(databaseCSS ? '✅ 数据库样式CSS已加载' : '❌ 数据库样式CSS未找到');
        
        if (databases.length > 0) {
            databases.forEach((db, index) => {
                const title = db.querySelector('.notion-database-title');
                const content = db.querySelector('.notion-table, .notion-gallery-grid, .notion-board-columns');
                console.log(`数据库 ${index + 1}: ${title ? title.textContent : '无标题'} - ${content ? '有内容' : '无内容'}`);
            });
        }
    }
    
    // 检查控制台错误
    function checkConsoleErrors() {
        console.log('\n⚠️ [控制台错误检查]');
        console.log('请查看控制台中是否有以下类型的错误:');
        console.log('- SSL_ERROR_SYSCALL (网络连接问题)');
        console.log('- 404错误 (资源未找到)');
        console.log('- KaTeX/Mermaid相关错误');
        console.log('- CSS压缩相关警告');
    }
    
    // 提供修复建议
    function provideSuggestions() {
        console.log('\n💡 [修复建议]');
        console.log('如果发现问题，请尝试以下步骤:');
        console.log('1. 刷新页面，确保最新的修复已生效');
        console.log('2. 检查网络连接，确保可以访问CDN资源');
        console.log('3. 如果仍有问题，请在控制台运行: window.NotionResourceChecker.checkAllResources()');
        console.log('4. 对于SSL错误，插件已增加重试机制，请稍等片刻');
    }
    
    // 运行所有检查
    checkResourceOptimizer();
    checkKaTeX();
    checkMermaid();
    checkDatabaseViews();
    checkConsoleErrors();
    provideSuggestions();
    
    console.log('\n🎉 [诊断完成] 请查看上述结果并按建议操作');
    
})();
