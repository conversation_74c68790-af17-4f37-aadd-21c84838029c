/**
 * KaTeX字体测试脚本
 * 验证所有字体文件是否正确加载
 * 
 * @since 2.0.0-beta.1
 * @package Notion_To_WordPress
 */

console.log('🔤 [字体测试] 开始验证KaTeX字体加载...');

// 需要测试的字体文件列表
const fontFiles = [
    // 核心字体
    'KaTeX_Main-Regular.woff2',
    'KaTeX_Math-Italic.woff2',
    'KaTeX_Size2-Regular.woff2',
    
    // 100%支持字体
    'KaTeX_AMS-Regular.woff2',
    'KaTeX_Size1-Regular.woff2',
    'KaTeX_Size3-Regular.woff2',
    'KaTeX_Size4-Regular.woff2',
    'KaTeX_Main-Bold.woff2',
    'KaTeX_Main-Italic.woff2',
    'KaTeX_Caligraphic-Regular.woff2',
    'KaTeX_Script-Regular.woff2',
    'KaTeX_Typewriter-Regular.woff2',
    
    // 高级字体
    'KaTeX_Caligraphic-Bold.woff2',
    'KaTeX_Fraktur-Regular.woff2',
    'KaTeX_Fraktur-Bold.woff2',
    'KaTeX_Main-BoldItalic.woff2',
    'KaTeX_Math-BoldItalic.woff2',
    'KaTeX_SansSerif-Regular.woff2',
    'KaTeX_SansSerif-Bold.woff2',
    'KaTeX_SansSerif-Italic.woff2'
];

// 测试结果
const testResults = {
    loaded: [],
    failed: [],
    total: fontFiles.length
};

// 测试单个字体文件
async function testFontFile(fontFile) {
    const fontPath = `/wp-content/plugins/Notion-to-WordPress/assets/vendor/katex/fonts/${fontFile}`;
    
    try {
        const response = await fetch(fontPath);
        if (response.ok) {
            console.log(`✅ ${fontFile} - 加载成功 (${response.status})`);
            testResults.loaded.push(fontFile);
            return true;
        } else {
            console.log(`❌ ${fontFile} - 加载失败 (${response.status})`);
            testResults.failed.push(fontFile);
            return false;
        }
    } catch (error) {
        console.log(`❌ ${fontFile} - 网络错误: ${error.message}`);
        testResults.failed.push(fontFile);
        return false;
    }
}

// 测试字体渲染效果
function testFontRendering() {
    console.log('\n🎨 [字体渲染测试]');
    
    // 检查CSS是否加载
    const katexFonts = document.querySelector('link[href*="katex-fonts.css"]');
    console.log(katexFonts ? '✅ KaTeX字体样式CSS已加载' : '❌ KaTeX字体样式CSS未加载');
    
    // 检查字体是否在CSS中定义
    const fontFamilies = [
        'KaTeX_Main',
        'KaTeX_Math', 
        'KaTeX_AMS',
        'KaTeX_Size1',
        'KaTeX_Size2',
        'KaTeX_Size3',
        'KaTeX_Size4',
        'KaTeX_Caligraphic',
        'KaTeX_Script',
        'KaTeX_Typewriter',
        'KaTeX_Fraktur',
        'KaTeX_SansSerif'
    ];
    
    console.log('\n📝 检查字体族定义:');
    fontFamilies.forEach(family => {
        // 创建测试元素
        const testEl = document.createElement('div');
        testEl.style.fontFamily = family;
        testEl.style.position = 'absolute';
        testEl.style.visibility = 'hidden';
        testEl.textContent = 'Test';
        document.body.appendChild(testEl);
        
        const computedStyle = window.getComputedStyle(testEl);
        const actualFont = computedStyle.fontFamily;
        
        document.body.removeChild(testEl);
        
        if (actualFont.includes(family)) {
            console.log(`✅ ${family} - 字体族已定义`);
        } else {
            console.log(`⚠️ ${family} - 字体族未生效，使用备用字体: ${actualFont}`);
        }
    });
}

// 测试数学公式渲染
function testMathRendering() {
    console.log('\n🧮 [数学公式渲染测试]');
    
    const equations = document.querySelectorAll('.notion-equation-inline, .notion-equation-block');
    const renderedEquations = document.querySelectorAll('.katex');
    
    console.log(`📊 总公式数: ${equations.length}`);
    console.log(`📊 已渲染: ${renderedEquations.length}`);
    
    if (renderedEquations.length > 0) {
        // 检查渲染的公式是否使用了正确的字体
        const sampleEquation = renderedEquations[0];
        const katexElements = sampleEquation.querySelectorAll('*');
        
        let fontUsageCount = {};
        katexElements.forEach(el => {
            const computedStyle = window.getComputedStyle(el);
            const fontFamily = computedStyle.fontFamily;
            if (fontFamily.includes('KaTeX')) {
                const katexFont = fontFamily.match(/KaTeX_\w+/);
                if (katexFont) {
                    fontUsageCount[katexFont[0]] = (fontUsageCount[katexFont[0]] || 0) + 1;
                }
            }
        });
        
        console.log('📊 公式中使用的KaTeX字体:');
        Object.entries(fontUsageCount).forEach(([font, count]) => {
            console.log(`  ${font}: ${count}次`);
        });
    }
}

// 生成测试报告
function generateReport() {
    console.log('\n📋 [字体测试报告]');
    console.log('='.repeat(50));
    
    console.log(`✅ 成功加载: ${testResults.loaded.length}/${testResults.total} 个字体文件`);
    
    if (testResults.failed.length > 0) {
        console.log(`❌ 加载失败: ${testResults.failed.length} 个字体文件`);
        console.log('失败的文件:', testResults.failed);
    }
    
    const successRate = (testResults.loaded.length / testResults.total * 100).toFixed(1);
    console.log(`📊 成功率: ${successRate}%`);
    
    if (successRate >= 95) {
        console.log('🎉 字体配置完美！支持100%+的数学公式');
    } else if (successRate >= 80) {
        console.log('✅ 字体配置良好，支持大部分数学公式');
    } else {
        console.log('⚠️ 字体配置需要改进');
    }
    
    console.log('\n💡 [建议]');
    if (testResults.failed.length === 0) {
        console.log('- 所有字体文件都正确加载！');
        console.log('- 现在可以渲染所有类型的数学公式');
        console.log('- 不会再有404字体错误');
    } else {
        console.log('- 检查失败的字体文件是否正确复制');
        console.log('- 确认文件路径和权限设置');
    }
}

// 运行完整测试
async function runFontTest() {
    console.log('🚀 开始字体测试...\n');
    
    // 测试字体文件加载
    console.log('📁 [字体文件测试]');
    for (const fontFile of fontFiles) {
        await testFontFile(fontFile);
        // 小延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    // 测试字体渲染
    testFontRendering();
    
    // 测试数学公式
    testMathRendering();
    
    // 生成报告
    generateReport();
}

// 自动运行测试
runFontTest();

// 暴露测试函数
window.KaTeXFontTest = {
    runFontTest,
    testFontFile,
    testFontRendering,
    testMathRendering,
    getResults: () => testResults
};

console.log('\n🎉 [字体测试启动] 正在检查所有字体文件...');
