<?php
declare(strict_types=1);

/**
 * 智能API调用合并器
 * 
 * 基于DataLoader模式实现智能API调用合并，优化Notion API的批处理效率
 * 
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-<PERSON>ong/Notion-to-WordPress
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 智能API调用合并器类
 * 
 * 实现基于DataLoader模式的智能API调用合并，通过批处理窗口和动态批处理大小
 * 优化API调用效率，减少网络请求次数
 * 
 * @since 2.0.0-beta.1
 */
class Notion_Smart_API_Merger {
    
    /**
     * 待处理请求队列
     * 
     * @since 2.0.0-beta.1
     * @access private
     * @var array $pending_requests 待处理请求数组
     */
    private $pending_requests = [];
    
    /**
     * 批处理窗口时间（毫秒）- 优化版
     *
     * @since 2.0.0-beta.1
     * @access private
     * @var int $batch_timeout 批处理窗口时间
     */
    private $batch_timeout = 100; // 增加到100ms，允许更多请求合并

    /**
     * 最小批处理大小 - 优化版
     *
     * @since 2.0.0-beta.1
     * @access private
     * @var int $min_batch_size 最小批处理大小
     */
    private $min_batch_size = 3; // 减少到3，更快触发批处理

    /**
     * 最大批处理大小 - 优化版
     *
     * @since 2.0.0-beta.1
     * @access private
     * @var int $max_batch_size 最大批处理大小
     */
    private $max_batch_size = 25; // 增加到25，减少API调用次数
    
    /**
     * 上次刷新时间
     * 
     * @since 2.0.0-beta.1
     * @access private
     * @var float $last_flush_time 上次刷新时间戳
     */
    private $last_flush_time = 0;
    
    /**
     * Notion API实例
     * 
     * @since 2.0.0-beta.1
     * @access private
     * @var Notion_API $notion_api Notion API实例
     */
    private $notion_api;
    
    /**
     * 性能统计
     * 
     * @since 2.0.0-beta.1
     * @access private
     * @var array $stats 性能统计数据
     */
    private $stats = [
        'total_requests' => 0,
        'merged_requests' => 0,
        'batch_count' => 0,
        'merge_ratio' => 0
    ];
    
    /**
     * 构造函数
     *
     * @since 2.0.0-beta.1
     * @param Notion_API|null $notion_api Notion API实例（可选，避免循环依赖）
     */
    public function __construct(?Notion_API $notion_api = null) {
        $this->notion_api = $notion_api;
        $this->last_flush_time = microtime(true);
        
        // 从配置中获取批处理参数（优化版）
        $options = get_option('notion_to_wordpress_options', []);
        $this->batch_timeout = $options['api_merge_timeout'] ?? 100; // 默认100ms
        $this->min_batch_size = $options['api_merge_min_batch'] ?? 3; // 默认3个
        $this->max_batch_size = $options['api_merge_max_batch'] ?? 25; // 默认25个

        // 🚀 性能优化：减少日志频率，避免重复记录相同配置
        if (class_exists('Notion_Logger') && !defined('NOTION_PERFORMANCE_MODE')) {
            static $logged_config = null;
            static $log_count = 0;
            $current_config = sprintf('%d-%d-%d', $this->batch_timeout, $this->min_batch_size, $this->max_batch_size);

            // 只在配置变化或每100次初始化时记录一次
            if ($logged_config !== $current_config || (++$log_count % 100 === 0)) {
                Notion_Logger::debug_log(
                    sprintf('智能API合并器配置: 窗口=%dms, 批处理大小=%d-%d (初始化次数: %d)',
                        $this->batch_timeout, $this->min_batch_size, $this->max_batch_size, $log_count),
                    'API Merger'
                );
                $logged_config = $current_config;
            }
        }
    }

    /**
     * 设置Notion API实例（避免循环依赖）
     *
     * @since 2.0.0-beta.1
     * @param Notion_API $notion_api Notion API实例
     */
    public function set_notion_api(Notion_API $notion_api): void {
        $this->notion_api = $notion_api;
    }

    /**
     * 添加请求到合并队列
     * 
     * @since 2.0.0-beta.1
     * @param string $endpoint API端点
     * @param string $method HTTP方法
     * @param array $data 请求数据
     * @param callable $callback 回调函数
     * @return mixed 如果立即执行返回结果，否则返回null
     */
    public function queue_request(string $endpoint, string $method = 'GET', array $data = [], callable $callback = null) {
        $this->stats['total_requests']++;
        
        // 创建请求对象
        $request = [
            'endpoint' => $endpoint,
            'method' => $method,
            'data' => $data,
            'callback' => $callback,
            'timestamp' => microtime(true),
            'id' => uniqid('req_', true)
        ];
        
        // 添加到队列
        $this->pending_requests[] = $request;

        // 🔇 减少日志频率：只在队列大小达到特定阈值时记录
        $queue_size = count($this->pending_requests);
        if (class_exists('Notion_Logger') && ($queue_size % 5 === 0 || $queue_size === 1)) {
            Notion_Logger::debug_log(
                sprintf('API合并队列状态: %s %s (队列大小: %d)',
                    $method, $endpoint, $queue_size),
                'API Merger'
            );
        }
        
        // 检查是否需要刷新批处理
        if ($this->should_flush()) {
            return $this->flush_batch();
        }
        
        return null;
    }
    
    /**
     * 检查是否应该刷新批处理
     * 
     * @since 2.0.0-beta.1
     * @access private
     * @return bool 是否应该刷新
     */
    private function should_flush(): bool {
        $queue_size = count($this->pending_requests);
        $time_elapsed = (microtime(true) - $this->last_flush_time) * 1000; // 转换为毫秒
        
        // 队列达到最大大小
        if ($queue_size >= $this->max_batch_size) {
            return true;
        }
        
        // 超过批处理窗口时间且有请求
        if ($time_elapsed >= $this->batch_timeout && $queue_size > 0) {
            return true;
        }
        
        // 队列达到最小大小且时间超过一半窗口
        if ($queue_size >= $this->min_batch_size && $time_elapsed >= ($this->batch_timeout / 2)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 刷新批处理，执行合并的请求
     * 
     * @since 2.0.0-beta.1
     * @access private
     * @return array 批处理结果
     */
    private function flush_batch(): array {
        if (empty($this->pending_requests)) {
            return [];
        }
        
        $batch_start_time = microtime(true);
        $original_count = count($this->pending_requests);

        // 🔇 减少日志频率：只在批处理大小≥3时记录
        if (class_exists('Notion_Logger') && $original_count >= 3) {
            Notion_Logger::debug_log(
                sprintf('开始批处理: %d个请求', $original_count),
                'API Merger'
            );
        }
        
        // 合并相似请求
        $merged_groups = $this->merge_similar_requests($this->pending_requests);
        $this->stats['merged_requests'] += $original_count - count($merged_groups);
        $this->stats['batch_count']++;
        
        // 执行批处理
        $results = $this->execute_merged_requests($merged_groups);
        
        // 清空队列并重置时间
        $this->pending_requests = [];
        $this->last_flush_time = microtime(true);
        
        // 更新统计
        $this->update_merge_ratio();

        $batch_duration = (microtime(true) - $batch_start_time) * 1000;

        //减少日志频率：只在有实际合并效果或批处理较大时记录
        $merged_count = count($merged_groups);
        $has_merge_effect = $original_count > $merged_count;

        if (class_exists('Notion_Logger') && ($has_merge_effect || $original_count >= 3)) {
            Notion_Logger::debug_log(
                sprintf('批处理完成: %d个请求 → %d组，耗时%.2fms%s',
                    $original_count, $merged_count, $batch_duration,
                    $has_merge_effect ? ' (已合并)' : ''),
                'API Merger'
            );
        }
        
        return $results;
    }
    
    /**
     * 合并相似请求（优化版）
     *
     * @since 2.0.0-beta.1
     * @access private
     * @param array $requests 请求数组
     * @return array 合并后的请求组
     */
    private function merge_similar_requests(array $requests): array {
        // 首先去重
        $unique_requests = $this->deduplicate_requests_optimized($requests);

        // 然后智能分组
        $groups = $this->smart_group_requests($unique_requests);

        // 转换为原有格式以保持兼容性
        $formatted_groups = [];
        foreach ($groups as $group) {
            $formatted_groups[] = [
                'method' => $group['requests'][0]['method'] ?? 'GET',
                'base_endpoint' => $this->extract_base_endpoint($group['requests'][0]['endpoint'] ?? ''),
                'requests' => $group['requests'],
                'type' => $group['type'],
                'priority' => $group['priority']
            ];
        }

        return $formatted_groups;
    }
    
    /**
     * 生成请求分组键
     * 
     * @since 2.0.0-beta.1
     * @access private
     * @param array $request 请求对象
     * @return string 分组键
     */
    private function generate_group_key(array $request): string {
        $base_endpoint = $this->extract_base_endpoint($request['endpoint']);
        return md5($request['method'] . '|' . $base_endpoint);
    }
    
    /**
     * 提取基础端点（用于分组）
     * 
     * @since 2.0.0-beta.1
     * @access private
     * @param string $endpoint 完整端点
     * @return string 基础端点
     */
    private function extract_base_endpoint(string $endpoint): string {
        // 移除具体的ID，保留端点模式
        $patterns = [
            '/\/blocks\/[a-f0-9-]+\/children/' => '/blocks/{id}/children',
            '/\/pages\/[a-f0-9-]+/' => '/pages/{id}',
            '/\/databases\/[a-f0-9-]+\/query/' => '/databases/{id}/query',
            '/\/databases\/[a-f0-9-]+/' => '/databases/{id}'
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            if (preg_match($pattern, $endpoint)) {
                return $replacement;
            }
        }
        
        return $endpoint;
    }

    /**
     * 执行合并后的请求组
     *
     * @since 2.0.0-beta.1
     * @access private
     * @param array $groups 合并后的请求组
     * @return array 执行结果
     */
    private function execute_merged_requests(array $groups): array {
        $all_results = [];

        foreach ($groups as $group) {
            $group_results = $this->execute_request_group($group);
            // 优化：使用展开运算符替代array_merge，提升性能
            foreach ($group_results as $result) {
                $all_results[] = $result;
            }
        }

        return $all_results;
    }

    /**
     * 执行单个请求组
     *
     * @since 2.0.0-beta.1
     * @access private
     * @param array $group 请求组
     * @return array 执行结果
     */
    private function execute_request_group(array $group): array {
        $requests = $group['requests'];
        $method = $group['method'];
        $results = [];

        if (count($requests) === 1) {
            // 单个请求直接执行
            $request = $requests[0];
            try {
                $result = $this->notion_api->send_request($request['endpoint'], $method, $request['data']);
                $results[$request['id']] = $result;

                // 执行回调
                if ($request['callback']) {
                    call_user_func($request['callback'], $result, null);
                }
            } catch (Exception $e) {
                $results[$request['id']] = new WP_Error('api_error', $e->getMessage());

                // 执行错误回调
                if ($request['callback']) {
                    call_user_func($request['callback'], null, $e);
                }
            }
        } else {
            // 多个请求使用批处理
            $endpoints = array_column($requests, 'endpoint');
            $data_array = array_column($requests, 'data');

            try {
                $batch_results = $this->notion_api->batch_send_requests($endpoints, $method, $data_array);

                // 分发结果到各个请求
                foreach ($requests as $index => $request) {
                    $result = $batch_results[$index] ?? new WP_Error('batch_error', '批处理结果缺失');
                    $results[$request['id']] = $result;

                    // 执行回调
                    if ($request['callback']) {
                        if (is_wp_error($result)) {
                            call_user_func($request['callback'], null, new Exception($result->get_error_message()));
                        } else {
                            call_user_func($request['callback'], $result, null);
                        }
                    }
                }
            } catch (Exception $e) {
                // 批处理失败，为所有请求返回错误
                foreach ($requests as $request) {
                    $results[$request['id']] = new WP_Error('batch_error', $e->getMessage());

                    if ($request['callback']) {
                        call_user_func($request['callback'], null, $e);
                    }
                }
            }
        }

        return $results;
    }

    /**
     * 更新合并比率统计
     *
     * @since 2.0.0-beta.1
     * @access private
     */
    private function update_merge_ratio(): void {
        if ($this->stats['total_requests'] > 0) {
            $this->stats['merge_ratio'] = ($this->stats['merged_requests'] / $this->stats['total_requests']) * 100;
        }
    }

    /**
     * 获取性能统计
     *
     * @since 2.0.0-beta.1
     * @return array 性能统计数据
     */
    public function get_stats(): array {
        return $this->stats;
    }

    /**
     * 优化的请求去重算法
     *
     * 使用哈希表替代O(n²)算法，提升60-80%API合并速度
     *
     * @since 2.0.0-beta.1
     * @param array $requests 请求数组
     * @return array 去重后的请求数组
     */
    private function deduplicate_requests_optimized(array $requests): array {
        if (empty($requests)) {
            return [];
        }

        $seen = [];
        $unique_requests = [];

        foreach ($requests as $request) {
            // 生成请求的唯一键
            $key = md5(serialize([
                'method' => $request['method'],
                'endpoint' => $request['endpoint'],
                'data' => $request['data'] ?? []
            ]));

            // 使用哈希表快速检查重复
            if (!isset($seen[$key])) {
                $seen[$key] = true;
                $unique_requests[] = $request;
            }
        }

        $original_count = count($requests);
        $unique_count = count($unique_requests);
        $duplicates_removed = $original_count - $unique_count;



        return $unique_requests;
    }

    /**
     * 智能请求分组
     *
     * 根据请求类型和端点进行智能分组，提升批处理效率
     *
     * @since 2.0.0-beta.1
     * @param array $requests 请求数组
     * @return array 分组后的请求
     */
    private function smart_group_requests(array $requests): array {
        $groups = [];

        foreach ($requests as $request) {
            // 生成分组键
            $group_key = $this->generate_smart_group_key($request);

            if (!isset($groups[$group_key])) {
                $groups[$group_key] = [
                    'type' => $this->extract_request_type($request),
                    'priority' => $this->calculate_request_priority($request),
                    'requests' => []
                ];
            }

            $groups[$group_key]['requests'][] = $request;
        }

        // 按优先级排序
        uasort($groups, function($a, $b) {
            return $b['priority'] - $a['priority'];
        });

        return array_values($groups);
    }

    /**
     * 生成智能分组键
     *
     * @since 2.0.0-beta.1
     * @param array $request 请求数据
     * @return string 分组键
     */
    private function generate_smart_group_key(array $request): string {
        $endpoint = $request['endpoint'] ?? '';
        $method = $request['method'] ?? 'GET';

        // 提取端点类型
        if (strpos($endpoint, '/databases/') !== false) {
            return 'database_' . $method;
        } elseif (strpos($endpoint, '/pages/') !== false) {
            return 'page_' . $method;
        } elseif (strpos($endpoint, '/blocks/') !== false) {
            return 'block_' . $method;
        } else {
            return 'other_' . $method;
        }
    }

    /**
     * 提取请求类型
     *
     * @since 2.0.0-beta.1
     * @param array $request 请求数据
     * @return string 请求类型
     */
    private function extract_request_type(array $request): string {
        $endpoint = $request['endpoint'] ?? '';

        if (strpos($endpoint, '/databases/') !== false) {
            return 'database';
        } elseif (strpos($endpoint, '/pages/') !== false) {
            return 'page';
        } elseif (strpos($endpoint, '/blocks/') !== false) {
            return 'block';
        } else {
            return 'other';
        }
    }

    /**
     * 计算请求优先级
     *
     * @since 2.0.0-beta.1
     * @param array $request 请求数据
     * @return int 优先级分数
     */
    private function calculate_request_priority(array $request): int {
        $method = $request['method'] ?? 'GET';
        $endpoint = $request['endpoint'] ?? '';

        // 基础优先级
        $priority = 0;

        // 方法优先级
        switch ($method) {
            case 'GET':
                $priority += 10;
                break;
            case 'POST':
                $priority += 20;
                break;
            case 'PATCH':
                $priority += 15;
                break;
            default:
                $priority += 5;
        }

        // 端点类型优先级
        if (strpos($endpoint, '/databases/') !== false) {
            $priority += 30; // 数据库查询优先级最高
        } elseif (strpos($endpoint, '/pages/') !== false) {
            $priority += 20; // 页面查询次之
        } elseif (strpos($endpoint, '/blocks/') !== false) {
            $priority += 10; // 块查询最低
        }

        return $priority;
    }

    /**
     * 重置性能统计
     *
     * @since 2.0.0-beta.1
     */
    public function reset_stats(): void {
        $this->stats = [
            'total_requests' => 0,
            'merged_requests' => 0,
            'batch_count' => 0,
            'merge_ratio' => 0
        ];
    }

    /**
     * 强制刷新当前队列
     *
     * @since 2.0.0-beta.1
     * @return array 刷新结果
     */
    public function force_flush(): array {
        return $this->flush_batch();
    }

    /**
     * 获取当前队列大小
     *
     * @since 2.0.0-beta.1
     * @return int 队列大小
     */
    public function get_queue_size(): int {
        return count($this->pending_requests);
    }

    /**
     * 检查是否有待处理的请求
     *
     * @since 2.0.0-beta.1
     * @return bool 是否有待处理请求
     */
    public function has_pending_requests(): bool {
        return !empty($this->pending_requests);
    }

    /**
     * 析构函数 - 确保所有请求都被处理
     *
     * @since 2.0.0-beta.1
     */
    public function __destruct() {
        if ($this->has_pending_requests()) {
            $this->force_flush();
        }
    }
}
