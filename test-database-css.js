/**
 * 测试数据库CSS加载的脚本
 * 在浏览器控制台中运行此脚本来验证数据库CSS是否正确加载
 * 
 * @since 2.0.0-beta.1
 * @package Notion_To_WordPress
 */

console.log('🧪 [数据库CSS测试] 开始测试...');

// 检查数据库CSS加载状态
function checkDatabaseCSS() {
    console.log('\n🎨 [数据库CSS检查]');
    
    // 检查CSS文件是否加载
    const databaseCSS = document.querySelector('link[href*="notion-database.css"]');
    if (databaseCSS) {
        console.log('✅ 数据库CSS文件已加载:', databaseCSS.href);
        
        // 检查CSS是否实际生效
        const testElement = document.createElement('div');
        testElement.className = 'notion-database';
        testElement.style.display = 'none';
        document.body.appendChild(testElement);
        
        const computedStyle = window.getComputedStyle(testElement);
        const hasStyles = computedStyle.background !== 'rgba(0, 0, 0, 0)' || 
                         computedStyle.border !== '0px none rgb(0, 0, 0)' ||
                         computedStyle.borderRadius !== '0px';
        
        document.body.removeChild(testElement);
        
        if (hasStyles) {
            console.log('✅ 数据库CSS样式已生效');
        } else {
            console.log('⚠️ 数据库CSS文件已加载但样式可能未生效');
        }
        
        return true;
    } else {
        console.log('❌ 数据库CSS文件未找到');
        return false;
    }
}

// 检查数据库元素
function checkDatabaseElements() {
    console.log('\n🗃️ [数据库元素检查]');
    
    const databases = document.querySelectorAll('.notion-database, .notion-child-database');
    console.log(`📊 找到 ${databases.length} 个数据库视图`);
    
    if (databases.length > 0) {
        databases.forEach((db, index) => {
            const title = db.querySelector('.notion-database-title');
            const content = db.querySelector('.notion-table, .notion-gallery-grid, .notion-board-columns');
            const hasStyles = window.getComputedStyle(db).background !== 'rgba(0, 0, 0, 0)';
            
            console.log(`数据库 ${index + 1}:`);
            console.log(`  标题: ${title ? title.textContent : '无标题'}`);
            console.log(`  内容: ${content ? '有内容' : '无内容'}`);
            console.log(`  样式: ${hasStyles ? '已应用' : '未应用'}`);
        });
        
        return databases.length;
    } else {
        console.log('ℹ️ 页面中未找到数据库视图');
        return 0;
    }
}

// 检查所有CSS文件
function checkAllCSS() {
    console.log('\n📋 [所有CSS文件检查]');
    
    const cssFiles = [
        { name: '自定义样式', pattern: 'custom-styles.css' },
        { name: 'LaTeX样式', pattern: 'latex-styles.css' },
        { name: '数据库样式', pattern: 'notion-database.css' },
        { name: 'KaTeX样式', pattern: 'katex.min.css' }
    ];
    
    cssFiles.forEach(css => {
        const element = document.querySelector(`link[href*="${css.pattern}"]`);
        if (element) {
            console.log(`✅ ${css.name}: ${element.href}`);
        } else {
            console.log(`❌ ${css.name}: 未找到`);
        }
    });
}

// 强制重新加载数据库CSS
function forceReloadDatabaseCSS() {
    console.log('\n🔄 [强制重新加载数据库CSS]');
    
    // 移除现有的数据库CSS
    const existingCSS = document.querySelector('link[href*="notion-database.css"]');
    if (existingCSS) {
        existingCSS.remove();
        console.log('🗑️ 已移除现有数据库CSS');
    }
    
    // 重新加载数据库CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = '/wp-content/plugins/Notion-to-WordPress/assets/css/notion-database.css?ver=' + Date.now();
    
    link.onload = function() {
        console.log('✅ 数据库CSS重新加载成功');
        
        // 重新检查样式
        setTimeout(() => {
            checkDatabaseElements();
        }, 100);
    };
    
    link.onerror = function() {
        console.log('❌ 数据库CSS重新加载失败');
    };
    
    document.head.appendChild(link);
    console.log('🔄 正在重新加载数据库CSS...');
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始运行数据库CSS测试...\n');
    
    const results = {
        cssLoaded: checkDatabaseCSS(),
        elementsFound: checkDatabaseElements() > 0
    };
    
    checkAllCSS();
    
    console.log('\n📋 [测试结果汇总]');
    console.log(`${results.cssLoaded ? '✅' : '❌'} CSS文件加载: ${results.cssLoaded ? '成功' : '失败'}`);
    console.log(`${results.elementsFound ? '✅' : '❌'} 数据库元素: ${results.elementsFound ? '找到' : '未找到'}`);
    
    if (!results.cssLoaded) {
        console.log('\n💡 [修复建议]');
        console.log('1. 运行 forceReloadDatabaseCSS() 强制重新加载CSS');
        console.log('2. 检查插件文件是否完整');
        console.log('3. 刷新页面重试');
    }
    
    return results;
}

// 自动运行测试
const testResults = runAllTests();

// 暴露测试函数供手动调用
window.DatabaseCSSTest = {
    runAllTests,
    checkDatabaseCSS,
    checkDatabaseElements,
    checkAllCSS,
    forceReloadDatabaseCSS
};

console.log('\n🎉 [数据库CSS测试完成] 可手动调用 DatabaseCSSTest.forceReloadDatabaseCSS() 强制重新加载');
