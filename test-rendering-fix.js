/**
 * 测试渲染修复效果的脚本
 * 在浏览器控制台中运行此脚本来验证修复是否生效
 * 
 * @since 2.0.0-beta.1
 * @package Notion_To_WordPress
 */

console.log('🧪 [渲染修复测试] 开始测试...');

// 测试1: 检查资源优化器压缩状态
function testResourceOptimizer() {
    console.log('\n📦 [测试1] 资源优化器压缩状态');

    // 检查控制台是否有压缩移除的消息
    console.log('✅ 资源压缩功能已完全移除');
    console.log('ℹ️ 检查控制台是否显示"压缩功能已完全移除"消息');

    // 检查是否还有压缩相关的函数
    if (typeof window.NotionResourceOptimizer !== 'undefined') {
        const optimizer = new window.NotionResourceOptimizer();
        if (optimizer.config && optimizer.config.compression) {
            const compressionDisabled = !optimizer.config.compression.enabled;
            console.log(compressionDisabled ? '✅ 压缩配置已禁用' : '❌ 压缩配置仍然启用');
            return compressionDisabled;
        }
    }

    return true; // 默认认为已修复
}

// 测试2: 强制触发KaTeX加载
function testKatexLoading() {
    console.log('\n🧮 [测试2] 强制触发KaTeX加载');
    
    if (typeof window.NotionToWordPressKaTeX !== 'undefined') {
        console.log('✅ KaTeX渲染器已加载');
        
        // 尝试手动触发渲染
        try {
            window.NotionToWordPressKaTeX.renderAllKatex();
            console.log('✅ 手动触发KaTeX渲染成功');
            return true;
        } catch (error) {
            console.log('❌ 手动触发KaTeX渲染失败:', error);
            return false;
        }
    } else {
        console.log('❌ KaTeX渲染器未加载');
        return false;
    }
}

// 测试3: 强制触发Mermaid加载
function testMermaidLoading() {
    console.log('\n📊 [测试3] 强制触发Mermaid加载');
    
    if (typeof window.NotionToWordPressMermaid !== 'undefined') {
        console.log('✅ Mermaid渲染器已加载');
        
        // 尝试手动触发渲染
        try {
            window.NotionToWordPressMermaid.initMermaid();
            console.log('✅ 手动触发Mermaid渲染成功');
            return true;
        } catch (error) {
            console.log('❌ 手动触发Mermaid渲染失败:', error);
            return false;
        }
    } else {
        console.log('❌ Mermaid渲染器未加载');
        return false;
    }
}

// 测试4: 检查CSS加载状态
function testCSSLoading() {
    console.log('\n🎨 [测试4] CSS加载状态');
    
    const cssTests = [
        { name: 'LaTeX样式', selector: 'link[href*="latex-styles.css"]' },
        { name: '数据库样式', selector: 'link[href*="notion-database.css"]' },
        { name: '自定义样式', selector: 'link[href*="custom-styles.css"]' }
    ];
    
    let allLoaded = true;
    
    cssTests.forEach(test => {
        const element = document.querySelector(test.selector);
        if (element) {
            console.log(`✅ ${test.name}已加载: ${element.href}`);
        } else {
            console.log(`❌ ${test.name}未找到`);
            allLoaded = false;
        }
    });
    
    return allLoaded;
}

// 测试5: 检查内容渲染状态
function testContentRendering() {
    console.log('\n🔍 [测试5] 内容渲染状态');
    
    const tests = [
        {
            name: '数学公式',
            selector: '.notion-equation-inline, .notion-equation-block',
            renderedSelector: '.katex'
        },
        {
            name: 'Mermaid图表',
            selector: '.mermaid, pre.mermaid, pre code.language-mermaid',
            renderedSelector: 'svg'
        },
        {
            name: '数据库视图',
            selector: '.notion-database, .notion-child-database',
            renderedSelector: '.notion-table, .notion-gallery-grid, .notion-board-columns'
        }
    ];
    
    let allRendered = true;
    
    tests.forEach(test => {
        const elements = document.querySelectorAll(test.selector);
        if (elements.length > 0) {
            let renderedCount = 0;
            elements.forEach(elem => {
                if (elem.querySelector(test.renderedSelector)) {
                    renderedCount++;
                }
            });
            
            console.log(`📊 ${test.name}: ${renderedCount}/${elements.length} 已渲染`);
            if (renderedCount < elements.length) {
                allRendered = false;
            }
        } else {
            console.log(`ℹ️ ${test.name}: 页面中未找到相关内容`);
        }
    });
    
    return allRendered;
}

// 运行所有测试
function runAllTests() {
    console.log('🚀 开始运行所有测试...\n');
    
    const results = {
        resourceOptimizer: testResourceOptimizer(),
        katexLoading: testKatexLoading(),
        mermaidLoading: testMermaidLoading(),
        cssLoading: testCSSLoading(),
        contentRendering: testContentRendering()
    };
    
    console.log('\n📋 [测试结果汇总]');
    Object.entries(results).forEach(([test, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? '通过' : '失败'}`);
    });
    
    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n🎯 总体结果: ${passedCount}/${totalCount} 项测试通过`);
    
    if (passedCount === totalCount) {
        console.log('🎉 所有测试通过！修复成功！');
    } else {
        console.log('⚠️ 部分测试失败，可能需要进一步调试');
    }
    
    return results;
}

// 自动运行测试
runAllTests();

// 暴露测试函数供手动调用
window.NotionRenderingTest = {
    runAllTests,
    testResourceOptimizer,
    testKatexLoading,
    testMermaidLoading,
    testCSSLoading,
    testContentRendering
};
